<!DOCTYPE html>
<html lang="es">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Prueba de Actualización de Perfil</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .form-group { margin-bottom: 15px; }
        label { display: block; margin-bottom: 5px; font-weight: bold; }
        input, select, textarea { width: 100%; padding: 8px; border: 1px solid #ddd; border-radius: 4px; }
        button { background: #007bff; color: white; padding: 10px 20px; border: none; border-radius: 4px; cursor: pointer; }
        button:hover { background: #0056b3; }
        .result { margin-top: 20px; padding: 10px; border-radius: 4px; }
        .success { background: #d4edda; color: #155724; border: 1px solid #c3e6cb; }
        .error { background: #f8d7da; color: #721c24; border: 1px solid #f5c6cb; }
    </style>
</head>
<body>
    <h1>Prueba de Actualización de Perfil de Administrador</h1>
    
    <h2>1. Actualizar Información Personal</h2>
    <form id="form-personal">
        <div class="form-group">
            <label for="nombres">Nombres:</label>
            <input type="text" id="nombres" name="nombres" value="Patricia Elena" required>
        </div>
        <div class="form-group">
            <label for="apellido_paterno">Apellido Paterno:</label>
            <input type="text" id="apellido_paterno" name="apellido_paterno" value="Molina" required>
        </div>
        <div class="form-group">
            <label for="apellido_materno">Apellido Materno:</label>
            <input type="text" id="apellido_materno" name="apellido_materno" value="Aguirre" required>
        </div>
        <div class="form-group">
            <label for="dni">DNI:</label>
            <input type="text" id="dni" name="dni" value="12345678" maxlength="8">
        </div>
        <div class="form-group">
            <label for="fecha_nacimiento">Fecha de Nacimiento:</label>
            <input type="date" id="fecha_nacimiento" name="fecha_nacimiento" value="1980-05-15">
        </div>
        <div class="form-group">
            <label for="sexo">Sexo:</label>
            <select id="sexo" name="sexo" required>
                <option value="femenino" selected>Femenino</option>
                <option value="masculino">Masculino</option>
            </select>
        </div>
        <div class="form-group">
            <label for="direccion">Dirección:</label>
            <input type="text" id="direccion" name="direccion" value="Av. Educación 123, San Isidro, Lima">
        </div>
        <div class="form-group">
            <label for="telefono">Teléfono:</label>
            <input type="tel" id="telefono" name="telefono" value="991663041">
        </div>
        <input type="hidden" name="tipo_actualizacion" value="personal">
        <button type="submit">Actualizar Información Personal</button>
    </form>
    
    <h2>2. Actualizar Información Profesional</h2>
    <form id="form-professional">
        <div class="form-group">
            <label for="cargo">Cargo:</label>
            <input type="text" id="cargo" name="cargo" value="Directora General" required>
        </div>
        <div class="form-group">
            <label for="departamento">Departamento:</label>
            <input type="text" id="departamento" name="departamento" value="Dirección Académica">
        </div>
        <div class="form-group">
            <label for="fecha_contratacion">Fecha de Contratación:</label>
            <input type="date" id="fecha_contratacion" name="fecha_contratacion" value="2020-01-15">
        </div>
        <input type="hidden" name="tipo_actualizacion" value="professional">
        <button type="submit">Actualizar Información Profesional</button>
    </form>
    
    <h2>3. Actualizar Contacto de Emergencia</h2>
    <form id="form-emergency">
        <div class="form-group">
            <label for="nombre_contacto">Nombre del Contacto:</label>
            <input type="text" id="nombre_contacto" name="nombre_contacto" value="Carlos Molina (Esposo)" required>
        </div>
        <div class="form-group">
            <label for="telefono_principal">Teléfono Principal:</label>
            <input type="tel" id="telefono_principal" name="telefono_principal" value="987654321" required>
        </div>
        <div class="form-group">
            <label for="telefono_alternativo">Teléfono Alternativo:</label>
            <input type="tel" id="telefono_alternativo" name="telefono_alternativo" value="991234567">
        </div>
        <div class="form-group">
            <label for="email">Email:</label>
            <input type="email" id="email" name="email" value="<EMAIL>">
        </div>
        <input type="hidden" name="tipo_actualizacion" value="emergency">
        <button type="submit">Actualizar Contacto de Emergencia</button>
    </form>
    
    <div id="result"></div>
    
    <script>
        // Función para enviar formularios
        function enviarFormulario(form) {
            const formData = new FormData(form);
            const resultDiv = document.getElementById('result');
            
            // Mostrar datos que se van a enviar
            console.log('Enviando datos:');
            for (let pair of formData.entries()) {
                console.log(pair[0] + ': ' + pair[1]);
            }
            
            fetch('perfil_a.php', {
                method: 'POST',
                body: formData
            })
            .then(response => {
                console.log('Status:', response.status);
                return response.text();
            })
            .then(text => {
                console.log('Respuesta completa:', text);
                
                try {
                    const data = JSON.parse(text);
                    if (data.success) {
                        resultDiv.innerHTML = `<div class="result success">✅ ${data.message}</div>`;
                    } else {
                        resultDiv.innerHTML = `<div class="result error">❌ Error: ${data.message}</div>`;
                    }
                } catch (e) {
                    console.error('Error parsing JSON:', e);
                    resultDiv.innerHTML = `<div class="result error">❌ Error en respuesta del servidor: ${text}</div>`;
                }
            })
            .catch(error => {
                console.error('Error:', error);
                resultDiv.innerHTML = `<div class="result error">❌ Error de conexión: ${error.message}</div>`;
            });
        }
        
        // Agregar event listeners a los formularios
        document.getElementById('form-personal').addEventListener('submit', function(e) {
            e.preventDefault();
            enviarFormulario(this);
        });
        
        document.getElementById('form-professional').addEventListener('submit', function(e) {
            e.preventDefault();
            enviarFormulario(this);
        });
        
        document.getElementById('form-emergency').addEventListener('submit', function(e) {
            e.preventDefault();
            enviarFormulario(this);
        });
    </script>
</body>
</html>
