<?php
// Archivo de prueba para verificar el funcionamiento del PerfilController
error_reporting(E_ALL);
ini_set('display_errors', 1);

echo "=== PRUEBA DE PERFILCONTROLLER ===\n";

// Verificar si los archivos existen
$archivos = [
    '../Modelo/Conexion.php',
    'AuthController.php',
    'PerfilController.php'
];

foreach ($archivos as $archivo) {
    if (file_exists($archivo)) {
        echo "✅ $archivo - EXISTE\n";
    } else {
        echo "❌ $archivo - NO EXISTE\n";
    }
}

// Intentar incluir los archivos
try {
    require_once '../Modelo/Conexion.php';
    echo "✅ Conexion.php incluido correctamente\n";
} catch (Exception $e) {
    echo "❌ Error incluyendo Conexion.php: " . $e->getMessage() . "\n";
}

try {
    require_once 'AuthController.php';
    echo "✅ AuthController.php incluido correctamente\n";
} catch (Exception $e) {
    echo "❌ Error incluyendo AuthController.php: " . $e->getMessage() . "\n";
}

// Probar conexión a la base de datos
try {
    $pdo = Conexion::getConexion();
    echo "✅ Conexión a la base de datos exitosa\n";
} catch (Exception $e) {
    echo "❌ Error conectando a la base de datos: " . $e->getMessage() . "\n";
}

// Simular una solicitud POST
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    echo "\n=== PROCESANDO SOLICITUD POST ===\n";
    echo "Datos recibidos:\n";
    print_r($_POST);
    
    try {
        require_once 'PerfilController.php';
        $controller = new PerfilController();
        $controller->procesarSolicitud();
    } catch (Exception $e) {
        echo "❌ Error en PerfilController: " . $e->getMessage() . "\n";
    }
} else {
    echo "\n=== PARA PROBAR ===\n";
    echo "Envía una solicitud POST a este archivo con los datos del formulario\n";
}
?>
