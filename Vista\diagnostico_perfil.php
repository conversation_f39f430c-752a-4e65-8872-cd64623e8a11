<?php
// Archivo de diagnóstico para verificar el estado del sistema
error_reporting(E_ALL);
ini_set('display_errors', 1);

echo "<h1>Diagnóstico del Sistema de Perfil</h1>";

// 1. Verificar archivos necesarios
echo "<h2>1. Verificación de Archivos</h2>";
$archivos = [
    '../Controlador/AuthController.php',
    '../Modelo/Administrador.php', 
    '../Modelo/Conexion.php',
    'perfil_a.php',
    'Js/perfil_a.js'
];

foreach ($archivos as $archivo) {
    if (file_exists($archivo)) {
        echo "✅ $archivo - EXISTE<br>";
    } else {
        echo "❌ $archivo - NO EXISTE<br>";
    }
}

// 2. Verificar conexión a base de datos
echo "<h2>2. Verificación de Base de Datos</h2>";
try {
    require_once '../Modelo/Conexion.php';
    $pdo = Conexion::getConexion();
    echo "✅ Conexión a base de datos exitosa<br>";
    
    // Verificar tablas necesarias
    $tablas = ['usuarios', 'personas', 'administradores', 'contactos_emergencia'];
    foreach ($tablas as $tabla) {
        $stmt = $pdo->query("SHOW TABLES LIKE '$tabla'");
        if ($stmt->rowCount() > 0) {
            echo "✅ Tabla '$tabla' existe<br>";
        } else {
            echo "❌ Tabla '$tabla' NO existe<br>";
        }
    }
    
} catch (Exception $e) {
    echo "❌ Error de conexión: " . $e->getMessage() . "<br>";
}

// 3. Verificar usuario administrador
echo "<h2>3. Verificación de Usuario Administrador</h2>";
try {
    $stmt = $pdo->query("SELECT u.*, p.nombres, p.apellido_paterno FROM usuarios u LEFT JOIN personas p ON u.id = p.usuario_id WHERE u.rol = 'administrador' LIMIT 1");
    $admin = $stmt->fetch();
    
    if ($admin) {
        echo "✅ Usuario administrador encontrado:<br>";
        echo "&nbsp;&nbsp;&nbsp;ID: " . $admin['id'] . "<br>";
        echo "&nbsp;&nbsp;&nbsp;Usuario: " . $admin['nombre_usuario'] . "<br>";
        echo "&nbsp;&nbsp;&nbsp;Email: " . $admin['email'] . "<br>";
        echo "&nbsp;&nbsp;&nbsp;Nombre: " . ($admin['nombres'] ?? 'No definido') . " " . ($admin['apellido_paterno'] ?? '') . "<br>";
    } else {
        echo "❌ No se encontró usuario administrador<br>";
    }
    
} catch (Exception $e) {
    echo "❌ Error verificando usuario: " . $e->getMessage() . "<br>";
}

// 4. Verificar sesión
echo "<h2>4. Verificación de Sesión</h2>";
session_start();

if (isset($_SESSION['usuario_id'])) {
    echo "✅ Sesión activa:<br>";
    echo "&nbsp;&nbsp;&nbsp;Usuario ID: " . $_SESSION['usuario_id'] . "<br>";
    echo "&nbsp;&nbsp;&nbsp;Rol: " . ($_SESSION['rol'] ?? 'No definido') . "<br>";
    echo "&nbsp;&nbsp;&nbsp;Nombre: " . ($_SESSION['nombres'] ?? 'No definido') . "<br>";
} else {
    echo "❌ No hay sesión activa<br>";
    echo "<p><strong>Para probar, necesitas:</strong></p>";
    echo "<ol>";
    echo "<li>Ir a <a href='../intranet.php'>intranet.php</a></li>";
    echo "<li>Iniciar sesión como administrador</li>";
    echo "<li>Luego volver a <a href='perfil_a.php'>perfil_a.php</a></li>";
    echo "</ol>";
}

// 5. Prueba de actualización simple
echo "<h2>5. Prueba de Actualización</h2>";

if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['test_update'])) {
    try {
        if (!isset($_SESSION['usuario_id'])) {
            throw new Exception('No hay sesión activa');
        }
        
        $pdo->beginTransaction();
        
        // Prueba simple de actualización
        $stmt = $pdo->prepare("UPDATE personas SET telefono = ? WHERE usuario_id = ?");
        $nuevoTelefono = '999' . rand(100000, 999999);
        $stmt->execute([$nuevoTelefono, $_SESSION['usuario_id']]);
        
        $pdo->commit();
        
        echo "✅ Prueba de actualización exitosa. Nuevo teléfono: $nuevoTelefono<br>";
        
    } catch (Exception $e) {
        if ($pdo->inTransaction()) {
            $pdo->rollBack();
        }
        echo "❌ Error en prueba de actualización: " . $e->getMessage() . "<br>";
    }
}

// Formulario de prueba
if (isset($_SESSION['usuario_id'])) {
    echo "<form method='post'>";
    echo "<input type='hidden' name='test_update' value='1'>";
    echo "<button type='submit'>Ejecutar Prueba de Actualización</button>";
    echo "</form>";
}

// 6. Información del sistema
echo "<h2>6. Información del Sistema</h2>";
echo "PHP Version: " . phpversion() . "<br>";
echo "Server: " . $_SERVER['SERVER_SOFTWARE'] . "<br>";
echo "Document Root: " . $_SERVER['DOCUMENT_ROOT'] . "<br>";
echo "Current Script: " . $_SERVER['SCRIPT_NAME'] . "<br>";

// 7. Logs recientes
echo "<h2>7. Logs de Error Recientes</h2>";
$logFile = ini_get('error_log');
if ($logFile && file_exists($logFile)) {
    $logs = file($logFile);
    $recentLogs = array_slice($logs, -10); // Últimas 10 líneas
    echo "<pre style='background: #f5f5f5; padding: 10px; max-height: 200px; overflow-y: auto;'>";
    foreach ($recentLogs as $log) {
        if (strpos($log, 'PERFIL UPDATE') !== false) {
            echo htmlspecialchars($log);
        }
    }
    echo "</pre>";
} else {
    echo "No se pudo acceder al archivo de logs<br>";
}

?>

<style>
body { font-family: Arial, sans-serif; margin: 20px; }
h1 { color: #333; }
h2 { color: #666; border-bottom: 1px solid #ddd; padding-bottom: 5px; }
button { background: #007bff; color: white; padding: 10px 20px; border: none; border-radius: 4px; cursor: pointer; }
button:hover { background: #0056b3; }
</style>
