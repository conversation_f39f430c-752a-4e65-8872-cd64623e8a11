<?php
// Archivo de prueba simple para verificar la funcionalidad de actualización
session_start();

// Simular sesión de administrador para pruebas
if (!isset($_SESSION['usuario_id'])) {
    $_SESSION['usuario_id'] = 1;
    $_SESSION['nombre_usuario'] = 'admin';
    $_SESSION['email'] = '<EMAIL>';
    $_SESSION['rol'] = 'administrador';
    $_SESSION['nombres'] = 'Administrador';
    $_SESSION['apellido_paterno'] = 'Sistema';
    $_SESSION['apellido_materno'] = 'Escuela';
}

require_once '../Modelo/Conexion.php';

// Procesar solicitud AJAX
if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['tipo_actualizacion'])) {
    header('Content-Type: application/json');
    
    try {
        $pdo = Conexion::getConexion();
        $tipoActualizacion = $_POST['tipo_actualizacion'];
        
        echo json_encode([
            'success' => true, 
            'message' => "Prueba exitosa para tipo: $tipoActualizacion",
            'datos_recibidos' => $_POST
        ]);
        
    } catch (Exception $e) {
        echo json_encode(['success' => false, 'message' => $e->getMessage()]);
    }
    exit;
}
?>

<!DOCTYPE html>
<html lang="es">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Prueba Simple de Actualización</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .form-group { margin-bottom: 15px; }
        label { display: block; margin-bottom: 5px; }
        input { width: 300px; padding: 8px; border: 1px solid #ddd; }
        button { background: #007bff; color: white; padding: 10px 20px; border: none; cursor: pointer; }
        .result { margin-top: 20px; padding: 10px; border-radius: 4px; }
        .success { background: #d4edda; color: #155724; }
        .error { background: #f8d7da; color: #721c24; }
    </style>
</head>
<body>
    <h1>Prueba Simple de Actualización de Perfil</h1>
    
    <form id="test-form">
        <div class="form-group">
            <label for="nombres">Nombres:</label>
            <input type="text" id="nombres" name="nombres" value="Patricia Elena" required>
        </div>
        <div class="form-group">
            <label for="apellido_paterno">Apellido Paterno:</label>
            <input type="text" id="apellido_paterno" name="apellido_paterno" value="Molina" required>
        </div>
        <div class="form-group">
            <label for="apellido_materno">Apellido Materno:</label>
            <input type="text" id="apellido_materno" name="apellido_materno" value="Aguirre" required>
        </div>
        
        <input type="hidden" name="tipo_actualizacion" value="personal">
        <button type="submit">Probar Actualización</button>
    </form>
    
    <div id="result"></div>
    
    <script>
        document.getElementById('test-form').addEventListener('submit', function(e) {
            e.preventDefault();
            
            const formData = new FormData(this);
            const resultDiv = document.getElementById('result');
            
            console.log('Enviando datos de prueba...');
            
            fetch('test_simple.php', {
                method: 'POST',
                body: formData
            })
            .then(response => {
                console.log('Status:', response.status);
                return response.text();
            })
            .then(text => {
                console.log('Respuesta:', text);
                
                try {
                    const data = JSON.parse(text);
                    if (data.success) {
                        resultDiv.innerHTML = `<div class="result success">✅ ${data.message}</div>`;
                        console.log('Datos recibidos:', data.datos_recibidos);
                    } else {
                        resultDiv.innerHTML = `<div class="result error">❌ ${data.message}</div>`;
                    }
                } catch (e) {
                    resultDiv.innerHTML = `<div class="result error">❌ Error: ${text}</div>`;
                }
            })
            .catch(error => {
                console.error('Error:', error);
                resultDiv.innerHTML = `<div class="result error">❌ Error de conexión: ${error.message}</div>`;
            });
        });
    </script>
</body>
</html>
